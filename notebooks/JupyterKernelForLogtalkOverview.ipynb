%versions

current_logtalk_flag(version_data, VersionData).

X = [1,2,3], list::append(X, [4,5,6], Z).

current_logtalk_flag(unicode, Unicode).
current_logtalk_flag(encoding_directive, EncodingDirective).

%bindings

forall(list::member(X, $Z), write(X)).


%queries

%%user

my_append([], <PERSON><PERSON>, Res).
my_append([H|T], List, [H|Res]) :-
  my_append(T, List, Res).

my_append([1,2], [3,4], R).

%%user

fact(a).
fact(b).

%%user

fact(c).
fact(d).

findall(X, fact(X), L).

%%user

:- discontiguous(a/1).
a(1).
a(2).

%%user+

b(3).
a(4).

listing(a/1), listing(b/1).

%%file foo.lgt

:- object(foo).

    :- public(bar/0).
    bar :-
        write('Hello world!\n').

:- end_object.

foo::bar.

%%highlight

:- object(hello_world).

	% the initialization/1 directive argument is automatically executed
	% when the object is loaded into memory:
	:- initialization(write('Hello World!\n')).

:- end_object.

list::member(M, [a,b,c]).

jupyter::retry.

retry.

findall(M, list::member(M, [a,b,c]), L).

set_logtalk_flag(debug, on), {elephants(loader)}.

fred::number_of_legs(N).

debugger::trace, fred::number_of_legs(N), debugger::notrace.

{planets(loader)}, debugger::spy(gravitational_acceleration/1), mars::weight(m2, W2).

debugger::nodebug, set_logtalk_flag(debug, off).

{ack(tester)}.

list::member(M, [1,2,3]).

jupyter::print_query_time.

%%table
list::member(Number, [10,20,30,40]), Square is Number*Number.

%%table
_List = [10,20,30,40], list::nth1(Position, _List, Element).

%%tsv flags.tsv
current_logtalk_flag(Flag, Value).

%%data
Data = [type-bar, title-'Bar Graph', x-[10, 20, 30, 40, 50, 60], height-[13, 45, 23, 34, 96, 76], color-dodgerblue, width-5, text-[x-15, y-85, s-'Blue bars!', fontdict-[color-mediumblue, style-italic, size-large]]].

%%data
Data = [type-barh, title-[label-'Horizontal bar graph', fontsize-18], y-['Apples','Oranges','Pears','Mangos'], width-[1,4,9,16], color-[orangered,orange,burlywood,khaki], bar_label-[label_type-center]]

%%data
Data = [type-pie, title-'Pie Graph', x-[35, 20, 30, 40, 50, 30], labels-['Apple', 'Bananna', 'Grapes', 'Orange', 'PineApple', 'Dragon Fruit'], autopct-'%.2f%%'].

%%data
logtalk_load(random(loader)),
random::sequence(1000, -20, 20, _List),
_Data = [type-hist, title-'Histogram', x-_List, bins-20, color-skyblue, edgecolor-black, xlabel-[label-'Values', loc-right, color-brown], ylabel-[label-'Frequency', loc-top, color-brown]].

%%data
Data = [type-scatter, title-'Scatter plot', x-[5,7,8,7,2,17,2,9,4,11,12,9,6], y-[99,86,87,88,111,86,103,87,94,78,77,85,86], grid-[color-navy, linestyle-'-', linewidth-0.2]].

%%data
Data = [type-plot, title-'Line plot', x-[1,2,3,4], y-[1,4,9,16], xticks-[ticks-[1,2,3,4], labels-[a,b,c,d]], annotate-[text-'Interesting value!', xy-[2,4], xytext-[3,4], arrowprops-[width-1, headwidth-4, facecolor-black, shrink-0.05]]].

%%data
Data = [type-loglog, title-'Log line plot', x-[1,2,3,4], y-[1,4,9,16]].

%%data
logtalk_load(types(loader)),
integer::sequence(0, 360, 10, _Xs),
findall(Y, (list::member(X,_Xs), Y is sin((X*pi)/180)), _Ys),
_Data = [type-stem, title-'Stem plot', x-_Xs, y-_Ys, xlabel-'X (degrees)', ylabel-'sin(X)'].

%%data
logtalk_load(random(loader)),
random::randseq(100, 0.0, 100.0, _Positions),
_Data = [type-eventplot, title-'Event plot', positions-_Positions, orientation-vertical, linelengths-1.4, color-cyan].

%%data
Data = [type-step, title-'Step plot', x-[1, 2, 3, 4, 5], y-[0, 1, 0, 2, 1], xlabel-'Values', ylabel-'Frequency'].

%%data

% List of Days
Days = [1, 2, 3, 4, 5], 
% Number of Study Hours
Studying = [7, 8, 6, 11, 7],
% Number of Playing Hours
Playing = [8, 5, 7, 8, 13],
% Stack plot with X, Y, colors value
Data = [type-stackplot, title-'Stack plot', x-Days, y-[Studying, Playing], labels-['Studying', 'Playing'], legend-[loc-'upper left'], colors-[orange, cyan], xlabel-'Days', ylabel-'No of Hours'].

%%data
logtalk_load(random(loader)),
findall(X, (integer::between(1,200,_), backend_random::random(80.0, 120.0, X)), _Xs),
findall(Y, (integer::between(1,200,_), backend_random::random(60.0, 90.0, Y)), _Ys),
findall(Z, (integer::between(1,200,_), backend_random::random(75.0, 105.0, Z)), _Zs),
_Data = [type-boxplot, title-'Box plot', x-[_Xs,_Ys,_Zs], positions-[2, 4, 6], widths-1.5, patch_artist-true, showmeans-false, showfliers-false].

%%data
Data = [type-errorbar, title-'Error bar', x-[2,4,6], y-[3.6,5.0,4.2], yerr-[0.9,1.2,0.5], fmt-o, linewidth-2, capsize-6, xlim-[left-0, right-8], xticks-[ticks-[1,2,3,4,5,6,7,8]], ylim-[bottom-0, top-8], yticks-[ticks-[1,2,3,4,5,6,7,8]]].

%%data
logtalk_load(random(loader)),
findall(X, (integer::between(1,5000,_), backend_random::random(-2.0, 2.0, X)), _Xs),
findall(Y, (list::member(X,_Xs), backend_random::random(-3.0, 3.0, Z), Y is 1.2*X + Z/3), _Ys),
_Data = [type-hexbin, title-'Hexbin plot', x-_Xs, y-_Ys, gridsize-20, xlim-[left-(-2), right-2], ylim-[bottom-(-3), top-3]].

%%data
logtalk_load(random(loader)),
findall(X, (integer::between(1,5000,_), backend_random::random(X1), X1 =\= 0.0, backend_random::random(X2), X is sqrt(-2.0 * log(X1)) * cos(2.0*pi*X2)), _Xs),
findall(Y, (list::member(X,_Xs), backend_random::random(Z1), Z1 =\= 0.0, backend_random::random(Z2), Z is sqrt(-2.0 * log(Z1)) * cos(2.0*pi*Z2), Y is 1.2*X + Z/3), _Ys),
float::sequence(-3.0,3.0,0.1,_Range,_),
_Data = [type-hist2d, title-'Hist2d plot', x-_Xs, y-_Ys, bins-[_Range, _Range], xlim-[left-(-2), right-2], ylim-[bottom-(-3), top-3]].

%%data
logtalk_load(random(loader)),
findall(X, (integer::between(1,5000,_), backend_random::random(-2.0, 2.0, X)), _Xs),
_Data = [type-ecdf, title-'ECDF plot', x-_Xs].

%%data
logtalk_load(random(loader)),
integer::sequence(-2, 2, _Is),
findall(F, (list::member(I,_Is), F is cos(I**2)), _Fs),
Data = [type-polar, title-[label-'Polar plot', fontweight-bold], theta-_Is, r-_Fs].

jupyter_widgets::create_text_input(name_input, 'Enter your name:', 'John Doe').

jupyter_widgets::get_widget_value(name_input, Name).

jupyter_widgets::create_password_input(password_input, 'Enter your password:').

jupyter_widgets::get_widget_value(password_input, Name).

jupyter_widgets::create_number_input(age_input, 'Enter your age:', 0, 120, 1, 25).

jupyter_widgets::get_widget_value(age_input, Age).

jupyter_widgets::create_number_input(x_input, 'Enter x:', 0.0, 10.0, 0.02, 5.0).

jupyter_widgets::get_widget_value(x_input, X).

jupyter_widgets::create_slider(temperature_slider, 'Temperature (°C)', -10, 40, 5, 20).

jupyter_widgets::get_widget_value(temperature_slider, Temperature).

jupyter_widgets::create_slider(pressure_slider, 'Pressure (kPa)', -10.5, 25.5, 0.1, 18.0).

jupyter_widgets::get_widget_value(pressure_slider, Pressure).

jupyter_widgets::create_date_input(birth_date_input, 'Enter your birth date:', '1990-01-01').

jupyter_widgets::get_widget_value(birth_date_input, BirthDate).

jupyter_widgets::create_time_input(meeting_time_input, 'Enter meeting time:', '14:00').


jupyter_widgets::get_widget_value(meeting_time_input, MeetingTime).

jupyter_widgets::create_email_input(email_input, 'Enter your email:', '<EMAIL>', '.+@.+\\..+').

jupyter_widgets::get_widget_value(email_input, Email).

jupyter_widgets::create_url_input(url_input, 'Enter a URL:', 'https://www.example.com', 'https?://.+').

jupyter_widgets::get_widget_value(url_input, URL).

jupyter_widgets::create_file_input(file_input, 'Select a file:').

jupyter_widgets::get_widget_value(file_input, File).

jupyter_widgets::create_color_input(color_input, 'Choose a color:', '#ff0000').

jupyter_widgets::get_widget_value(color_input, Color).

jupyter_widgets::create_dropdown(color_select, 'Choose a color:', [red, green, blue, yellow, purple]).

jupyter_widgets::get_widget_value(color_select, Color).

jupyter_widgets::create_checkbox(newsletter_checkbox, 'Subscribe to newsletter', false).

jupyter_widgets::get_widget_value(newsletter_checkbox, Color).

jupyter_widgets::create_button(action_button, 'Click Me!').

jupyter_widgets::get_widget_value(action_button, Clicked).

jupyter_widgets::widgets.

jupyter_forms::create_input_form(contact_form, [
    text_field(name, 'Full Name:', 'John Doe'),
    email_field(email, 'Email Address:', '<EMAIL>'),
    number_field(age, 'Age:', 20),
    select_field(country, 'Country:', [portugal, usa, canada, uk, germany, france], portugal),
    textarea_field(message, 'Message:', '', 4),
    checkbox_field(newsletter, 'Subscribe to newsletter:', false)
], [
    title('Contact Information'),
    submit_label('Submit Form'),
    cancel_label('Clear Form')
]).

jupyter_forms::get_form_data(contact_form, ContactData).

jupyter_forms::create_input_form(survey_form, [
    text_field(participant_id, 'Participant ID:', ''),
    select_field(experience, 'Programming Experience:', [beginner, intermediate, advanced], beginner),
    number_field(years_coding, 'Years of Coding:', 1),
    select_field(favorite_language, 'Favorite Language:', [python, java, javascript, prolog, logtalk], logtalk),
    textarea_field(comments, 'Additional Comments:', '', 3)
], [
    title('Programming Survey'),
    submit_label('Submit Survey')
]).

% Process survey results
jupyter_forms::get_form_data(survey_form, SurveyData).

%%tree
a(1, b(2, c(3, 4))).

%magic

jupyter::help.