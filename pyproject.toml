[build-system]
build-backend = "setuptools.build_meta"
requires = [
  "setuptools>=61.0"
]

[project]
name = "logtalk-jupyter-kernel"
version = "0.37.0"
authors = [
  { name = "<PERSON>" },
  { name = "<PERSON>" },
  { name = "<PERSON>" },
  { name = "d<PERSON><PERSON><PERSON>" }
]
maintainers = [
  { name = "<PERSON>", email = "<EMAIL>" }
]
description = "Hercutalk - A Jupyter Kernel for Logtalk"
readme = "README.md"
license = "MIT"
requires-python = ">=3.7"
classifiers = [
  "Development Status :: 4 - Beta",
  "Framework :: Jupyter",
  "Operating System :: OS Independent",
  "Intended Audience :: Developers",
  "Intended Audience :: Education",
  "Topic :: Scientific/Engineering",
  "Topic :: System :: Shells",
  "Natural Language :: English",
  "Programming Language :: Prolog",
]
keywords = [
  "logtalk",
  "prolog",
  "logic-programming",
]
dependencies = [
  "jupyter_client",
  "IPython",
  "ipykernel",
  "jupytext",
  "graphviz",
  "beautifulsoup4",
  "matplotlib"
]

[project.urls]
"Source" = "https://github.com/LogtalkDotOrg/logtalk-jupyter-kernel"
"Issues" = "https://github.com/LogtalkDotOrg/logtalk-jupyter-kernel/issues"

[project.optional-dependencies]
dev = [
  "jupyter_core",
  "jupyterlab",
  "notebook",
]
