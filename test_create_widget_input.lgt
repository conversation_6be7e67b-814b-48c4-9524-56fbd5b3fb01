%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
%  Test file for create_widget_input/3 predicate
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

:- initialization((
	% Test basic text input
	write('Testing create_widget_input/3 with text input...'), nl,
	jupyter_widgets::create_widget_input(test_text, 'Enter text:', [type-text, value-'default text', placeholder-'Type here']),
	
	% Test number input
	write('Testing create_widget_input/3 with number input...'), nl,
	jupyter_widgets::create_widget_input(test_number, 'Enter number:', [type-number, min-0, max-100, step-1, value-50]),
	
	% Test email input
	write('Testing create_widget_input/3 with email input...'), nl,
	jupyter_widgets::create_widget_input(test_email, 'Enter email:', [type-email, value-'<EMAIL>', required-true]),
	
	% Test checkbox input
	write('Testing create_widget_input/3 with checkbox input...'), nl,
	jupyter_widgets::create_widget_input(test_checkbox, 'Check me:', [type-checkbox, checked-true]),
	
	% Test date input
	write('Testing create_widget_input/3 with date input...'), nl,
	jupyter_widgets::create_widget_input(test_date, 'Select date:', [type-date, value-'2025-01-01']),
	
	% Test color input
	write('Testing create_widget_input/3 with color input...'), nl,
	jupyter_widgets::create_widget_input(test_color, 'Pick color:', [type-color, value-'#ff0000']),
	
	% Test range input
	write('Testing create_widget_input/3 with range input...'), nl,
	jupyter_widgets::create_widget_input(test_range, 'Select range:', [type-range, min-0, max-100, step-5, value-25]),
	
	% Test file input
	write('Testing create_widget_input/3 with file input...'), nl,
	jupyter_widgets::create_widget_input(test_file, 'Select file:', [type-file, accept-'.txt,.pdf']),
	
	% Test default (no type specified)
	write('Testing create_widget_input/3 with default type...'), nl,
	jupyter_widgets::create_widget_input(test_default, 'Default input:', [placeholder-'Default is text']),

	% Test custom style override
	write('Testing create_widget_input/3 with custom style...'), nl,
	jupyter_widgets::create_widget_input(test_custom_style, 'Custom styled input:', [type-text, style-'margin: 10px; padding: 10px; border: 2px solid red; border-radius: 5px;']),

	% Test default_style/1 predicate directly
	write('Testing default_style/1 predicate...'), nl,
	jupyter_widgets::default_style(DefaultStyle),
	format('Default style: ~w~n', [DefaultStyle]),

	write('All tests completed successfully!'), nl
)).
